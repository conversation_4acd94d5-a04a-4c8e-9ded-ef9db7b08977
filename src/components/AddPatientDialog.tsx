import { Facility } from "src/hooks/useFacilities";
import { <PERSON><PERSON>, <PERSON><PERSON>, YStack, Text, <PERSON>A<PERSON>, ScrollView } from "tamagui";
import FacilityDrawer from "./FacilityDrawer";
import PatientSearchDrawer from "./PatientSearchDrawer";
import { useState, useEffect, useRef } from "react";
import { PatientInfo } from "@/nurse/requestVisit";
import { Platform, Keyboard, KeyboardEvent } from "react-native";
import { usePatients } from "src/hooks/usePatientsByFacilities";
import { useAddpatientDialogStyle } from "./componentstyles/AddPatientDialogStyles";
import { Plus } from "@tamagui/lucide-icons";

interface AddPatientDialogProps {
  open: boolean;
  onClose: (open: boolean) => void;
  facilities: Facility[];
  facilitiesLoading: boolean;
  facilitiesError: any;
  ehrType: string;
  onAddpatient: (patient: PatientInfo) => void;
}

export function AddPatientDialog({
  open,
  onClose,
  facilities,
  facilitiesLoading,
  facilitiesError,
  ehrType,
  onAddpatient,
}: AddPatientDialogProps): JSX.Element {
  const [keyboardHeight, setKeyboardHeight] = useState<number>(0);
  const [patientNameSearch, setPatientNameSearch] = useState<string>("Al");
  const [selectedPatient, setSelectedPatient] = useState<PatientInfo>({
    id: "",
    name: "",
    dob: "",
    facilityId: "",
  });
  const [selectedFacilityId, setSelectedFacilityId] = useState<string>("");
  const [addPatientDisabled, setAddPatientDisabled] = useState<boolean>(true);

  const scrollViewRef = useRef<any>(null);
  const facilityRef = useRef<any>(null);
  const patientRef = useRef<any>(null);

  const styles = useAddpatientDialogStyle(keyboardHeight);
  const shouldUseScrollView = keyboardHeight > 0;

  // Keyboard event listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      (e: KeyboardEvent) => {
        setKeyboardHeight(e.endCoordinates.height);
      }
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        setKeyboardHeight(0);
      }
    );

    return () => {
      keyboardDidHideListener.remove();
      keyboardDidShowListener.remove();
    };
  }, []);

  // Auto-scroll to focused elements
  const scrollToElement = (elementRef: any, offset: number = 0) => {
    if (scrollViewRef.current && elementRef.current) {
      setTimeout(() => {
        elementRef.current.measureLayout(
          scrollViewRef.current,
          (_x: number, y: number) => {
            scrollViewRef.current.scrollTo({
              y: y - offset,
              animated: true,
            });
          },
          () => {}
        );
      }, 100);
    }
  };

  const handleFacilitySelect = (facilityId: string) => {
    setSelectedFacilityId(facilityId);
  };

  const {
    data: patients,
    isLoading: patientsLoading,
    error: patientsError,
  } = usePatients(selectedFacilityId || "", patientNameSearch);

  const handlePatientSelect = (
    patientId: string,
    patientFirstName: string,
    dob: string
  ) => {
    setSelectedPatient({
      id: patientId,
      name: patientFirstName,
      dob,
      facilityId: selectedFacilityId,
    });
    setAddPatientDisabled(false);
  };

  const addPatient = () => {
    onAddpatient(selectedPatient);
    onClose(false);
  };

  const dialogContent = (
    <>
      <Text {...styles.headerText}>Add a patient to call</Text>

      <YStack {...styles.facilityContainer} ref={facilityRef}>
        <Text {...styles.facilityTitle}>Facility</Text>
        <YStack>
          {facilitiesLoading ? (
            <Text>Loading facilities...</Text>
          ) : facilitiesError ? (
            <Text>Error loading facilities</Text>
          ) : (
            <FacilityDrawer
              data={facilities || []}
              placeholder="Select a Facility"
              onSelect={(id: string) => {
                handleFacilitySelect(id);
                scrollToElement(facilityRef, 50);
              }}
            />
          )}
        </YStack>
      </YStack>

      <YStack {...styles.facilityContainer} ref={patientRef}>
        {ehrType !== "manual" && (
          <Text {...styles.facilityTitle}>Patient</Text>
        )}
        {ehrType === "manual" ? (
          <YStack width="100%">
            <Text {...styles.facilityTitle}>Patient</Text>
            <TextArea
              {...styles.patientNameTextArea}
              placeholder="Type Patient Name"
              placeholderTextColor="$textcolor"
              overflow="hidden"
              value={selectedPatient.name}
              onFocus={() => scrollToElement(patientRef, 100)}
              onChangeText={(text) => {
                setSelectedPatient((prev) => ({
                  ...prev,
                  name: text,
                  id: text,
                }));
              }}
            />
            <Text {...styles.facilityTitle} mt={"$4"}>
              Date of Birth (MM/DD/YYYY)
            </Text>
            <TextArea
              {...styles.patientNameTextArea}
              placeholder="MM-DD-YYYY"
              placeholderTextColor="$textcolor"
              keyboardType={
                Platform.OS === "ios" ? "number-pad" : "numeric"
              }
              maxLength={10}
              value={selectedPatient.dob}
              onFocus={() => scrollToElement(patientRef, 150)}
              onChangeText={(text) => {
                const dob = formatDOB(text);
                setSelectedPatient((prev) => ({
                  ...prev,
                  dob,
                }));
              }}
            />
          </YStack>
        ) : (
          <YStack marginBlockEnd={50}>
            <PatientSearchDrawer
              data={patients || []}
              placeholder="Select a Patient"
              onSelect={(id: string, firstName: string, dob: string) => {
                handlePatientSelect(id, firstName, dob);
                scrollToElement(patientRef, 100);
              }}
              onSearch={(query: string) => {
                setPatientNameSearch(query);
                scrollToElement(patientRef, 100);
                return Promise.resolve();
              }}
              disabled={!selectedFacilityId}
              loading={patientsLoading}
              error={patientsError?.message || ""}
            />
          </YStack>
        )}
      </YStack>

      <Button
        icon={<Plus size={"$1"} color={"white"} />}
        {...(addPatientDisabled
          ? styles.addPatientBtnDisabled
          : styles.addPatientBtn)}
        disabled={addPatientDisabled}
        onPress={addPatient}
      >
        Add
      </Button>

      <Button {...styles.cancelBtn} onPress={() => onClose(false)}>
        Cancel
      </Button>
    </>
  );

  return (
    <Dialog modal open={open} onOpenChange={onClose}>
      <Dialog.Overlay {...styles.overlay} />
      <Dialog.Content {...styles.dialogContent}>
        {shouldUseScrollView ? (
          <ScrollView
            ref={scrollViewRef}
            {...styles.container}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {dialogContent}
          </ScrollView>
        ) : (
          <YStack {...styles.container}>
            {dialogContent}
          </YStack>
        )}
      </Dialog.Content>
    </Dialog>
  );
}

export const formatDOB = (raw: string) => {
  const digits = raw.replace(/\D/g, "").slice(0, 8);
  if (digits.length <= 2) return digits;
  if (digits.length <= 4) {
    return `${digits.slice(0, 2)}-${digits.slice(2)}`;
  }
  return `${digits.slice(0, 2)}-${digits.slice(2, 4)}-${digits.slice(4)}`;
};
