import { Facility } from "src/hooks/useFacilities";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>tack, Text, <PERSON>Area, ScrollView, View } from "tamagui";
import FacilityDrawer from "./FacilityDrawer";
import PatientSearchDrawer from "./PatientSearchDrawer";
import { useState, useRef } from "react";
import { PatientInfo } from "@/nurse/requestVisit";
import { Platform } from "react-native";
import { usePatients } from "src/hooks/usePatientsByFacilities";
import { useAddpatientDialogStyle } from "./componentstyles/AddPatientDialogStyles";
import { Plus } from "@tamagui/lucide-icons";

interface AddPatientDialogProps {
  open: boolean;
  onClose: (open: boolean) => void;
  facilities: Facility[];
  facilitiesLoading: boolean;
  facilitiesError: any;
  ehrType: string;
  onAddpatient: (patient: PatientInfo) => void;
}

export function AddPatientDialog({
  open,
  onClose,
  facilities,
  facilitiesLoading,
  facilitiesError,
  ehrType,
  onAddpatient,
}: AddPatientDialogProps): JSX.Element {
  const [patientNameSearch, setPatientNameSearch] = useState<string>("Al");
  const [selectedPatient, setSelectedPatient] = useState<PatientInfo>({
    id: "",
    name: "",
    dob: "",
    facilityId: "",
  });
  const [selectedFacilityId, setSelectedFacilityId] = useState<string>("");
  const [addPatientDisabled, setAddPatientDisabled] = useState<boolean>(true);

  const scrollViewRef = useRef<any>(null);
  const headerRef = useRef<any>(null);
  const facilityRef = useRef<any>(null);
  const patientTitleRef = useRef<any>(null);

  const styles = useAddpatientDialogStyle();

  // Auto-scroll to focused elements
  const scrollToFacility = () => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        y: 0, // Scroll to top to show facility
        animated: true,
      });
    }
  };

  const scrollToPatient = () => {
    if (scrollViewRef.current && headerRef.current && facilityRef.current && patientTitleRef.current) {
      setTimeout(() => {
        // Measure header height
        headerRef.current.measure((_x1: number, _y1: number, _width1: number, headerHeight: number) => {
          // Measure facility section height
          facilityRef.current.measure((_x2: number, _y2: number, _width2: number, facilityHeight: number) => {
            // Calculate total height to scroll past header and facility to show patient at top
            const scrollDistance = headerHeight + facilityHeight + 20; // +20 for gap
            scrollViewRef.current.scrollTo({
              y: scrollDistance,
              animated: true,
            });
          });
        });
      }, 100);
    } else {
      // Fallback with estimated heights
      if (scrollViewRef.current) {
        scrollViewRef.current.scrollTo({
          y: 180, // Estimated height of header + facility section
          animated: true,
        });
      }
    }
  };

  const handleFacilitySelect = (facilityId: string) => {
    setSelectedFacilityId(facilityId);
  };

  const {
    data: patients,
    isLoading: patientsLoading,
    error: patientsError,
  } = usePatients(selectedFacilityId || "", patientNameSearch);

  const handlePatientSelect = (
    patientId: string,
    patientFirstName: string,
    dob: string
  ) => {
    setSelectedPatient({
      id: patientId,
      name: patientFirstName,
      dob,
      facilityId: selectedFacilityId,
    });
    setAddPatientDisabled(false);
  };

  const addPatient = () => {
    onAddpatient(selectedPatient);
    onClose(false);
  };

  const dialogContent = (
    <>
      <View ref={headerRef}>
        <Text {...styles.headerText}>Add a patient to call</Text>
      </View>

      <YStack {...styles.facilityContainer} ref={facilityRef}>
        <Text {...styles.facilityTitle}>Facility</Text>
        <YStack>
          {facilitiesLoading ? (
            <Text>Loading facilities...</Text>
          ) : facilitiesError ? (
            <Text>Error loading facilities</Text>
          ) : (
            <FacilityDrawer
              data={facilities || []}
              placeholder="Select a Facility"
              onSelect={(id: string) => {
                handleFacilitySelect(id);
                scrollToFacility();
              }}
            />
          )}
        </YStack>
      </YStack>

      <YStack {...styles.facilityContainer}>
        <View ref={patientTitleRef}>
          {ehrType !== "manual" && (
            <Text {...styles.facilityTitle}>Patient</Text>
          )}
          {ehrType === "manual" && (
            <Text {...styles.facilityTitle}>Patient</Text>
          )}
        </View>
        {ehrType === "manual" ? (
          <YStack width="100%">
            <TextArea
              {...styles.patientNameTextArea}
              placeholder="Type Patient Name"
              placeholderTextColor="$textcolor"
              overflow="hidden"
              value={selectedPatient.name}
              onFocus={() => scrollToPatient()}
              onChangeText={(text) => {
                setSelectedPatient((prev) => ({
                  ...prev,
                  name: text,
                  id: text,
                }));
              }}
            />
            <Text {...styles.facilityTitle} mt={"$4"}>
              Date of Birth (MM/DD/YYYY)
            </Text>
            <TextArea
              {...styles.patientNameTextArea}
              placeholder="MM-DD-YYYY"
              placeholderTextColor="$textcolor"
              keyboardType={
                Platform.OS === "ios" ? "number-pad" : "numeric"
              }
              maxLength={10}
              value={selectedPatient.dob}
              onFocus={() => scrollToPatient()}
              onChangeText={(text) => {
                const dob = formatDOB(text);
                setSelectedPatient((prev) => ({
                  ...prev,
                  dob,
                }));
              }}
            />
          </YStack>
        ) : (
          <YStack marginBlockEnd={50}>
            <PatientSearchDrawer
              data={patients || []}
              placeholder="Select a Patient"
              onSelect={(id: string, firstName: string, dob: string) => {
                handlePatientSelect(id, firstName, dob);
                scrollToPatient();
              }}
              onSearch={(query: string) => {
                setPatientNameSearch(query);
                scrollToPatient();
                return Promise.resolve();
              }}
              disabled={!selectedFacilityId}
              loading={patientsLoading}
              error={patientsError?.message || ""}
            />
          </YStack>
        )}
      </YStack>

      <Button
        icon={<Plus size={"$1"} color={"white"} />}
        {...(addPatientDisabled
          ? styles.addPatientBtnDisabled
          : styles.addPatientBtn)}
        disabled={addPatientDisabled}
        onPress={addPatient}
      >
        Add
      </Button>

      <Button {...styles.cancelBtn} onPress={() => onClose(false)}>
        Cancel
      </Button>
    </>
  );

  return (
    <Dialog modal open={open} onOpenChange={onClose}>
      <Dialog.Overlay {...styles.overlay} />
      <Dialog.Content {...styles.dialogContent}>
        <ScrollView
          ref={scrollViewRef}
          {...styles.container}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {dialogContent}
        </ScrollView>
      </Dialog.Content>
    </Dialog>
  );
}

export const formatDOB = (raw: string) => {
  const digits = raw.replace(/\D/g, "").slice(0, 8);
  if (digits.length <= 2) return digits;
  if (digits.length <= 4) {
    return `${digits.slice(0, 2)}-${digits.slice(2)}`;
  }
  return `${digits.slice(0, 2)}-${digits.slice(2, 4)}-${digits.slice(4)}`;
};
