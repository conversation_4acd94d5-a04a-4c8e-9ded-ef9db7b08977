import { Facility } from "src/hooks/useFacilities";
import { <PERSON><PERSON>, <PERSON>ton, <PERSON>Stack, Text, TextArea, View } from "tamagui";
import FacilityDrawer, { SelectableItem } from "./FacilityDrawer";
import PatientSearchDrawer from "./PatientSearchDrawer";
import { useState, useRef } from "react";
import { PatientInfo } from "@/nurse/requestVisit";
import { Platform, ScrollView } from "react-native";
import { usePatients } from "src/hooks/usePatientsByFacilities";
import { useAddpatientDialogStyle } from "./componentstyles/AddPatientDialogStyles";
import { Plus } from "@tamagui/lucide-icons";


const facilitiesss: SelectableItem[] = [
  { id: "1", name: "Item One" },
  { id: "2", name: "Item Two" },
  { id: "3", name: "Item Three" },
  { id: "4", name: "Item Four" },
  { id: "5", name: "Item Five" },
  { id: "6", name: "Item Six" },
  { id: "7", name: "Item Seven" },
];

export interface SelectableItem1 {
  id: string;
  name: string;
  dob: string;
}

const patientsss: SelectableItem1[] = [
  { id: "1", name: "<PERSON>", dob: "1990-03-15" },
  { id: "2", name: "Bob <PERSON>", dob: "1985-07-22" },
  { id: "3", name: "<PERSON> <PERSON>", dob: "1992-11-05" },
  { id: "4", name: "<PERSON> <PERSON>", dob: "1988-01-10" },
  { id: "5", name: "<PERSON> <PERSON>", dob: "1995-06-30" },
  { id: "6", name: "Fiona Clark", dob: "1993-09-12" },
  { id: "7", name: "George Miller", dob: "1987-12-25" },
];


interface AddPatientDialogProps {
  open: boolean;
  onClose: (open: boolean) => void;
  facilities: Facility[];
  facilitiesLoading: boolean;
  facilitiesError: any;
  ehrType: string;
  onAddpatient: (patient: PatientInfo) => void;
}

export function AddPatientDialog({
  open,
  onClose,
  facilities,
  facilitiesLoading,
  facilitiesError,
  ehrType,
  onAddpatient,
}: AddPatientDialogProps): JSX.Element {
  const [patientNameSearch, setPatientNameSearch] = useState<string>("Al");
  const [selectedPatient, setSelectedPatient] = useState<PatientInfo>({
    id: "",
    name: "",
    dob: "",
    facilityId: "",
  });
  const [selectedFacilityId, setSelectedFacilityId] = useState<string>("");
  const [addPatientDisabled, setAddPatientDisabled] = useState<boolean>(true);

  const scrollViewRef = useRef<any>(null);
  const headerRef = useRef<any>(null);
  const facilityRef = useRef<any>(null);
  const patientTitleRef = useRef<any>(null);

  const styles = useAddpatientDialogStyle();

  // Auto-scroll to focused elements
  const scrollToFacility = () => {
    if (scrollViewRef.current) {
      scrollViewRef.current.scrollTo({
        y: 80, // Scroll to top to show facility
        animated: true,
      });
    }
  };

  const scrollToPatient = () => {
    console.log('scrollToPatient called');
    if (scrollViewRef.current) {
      console.log('ScrollView ref exists, scrolling...');
      setTimeout(() => {
        scrollViewRef.current.scrollTo({
          y: 150, // Scroll down to hide header and facility, show patient at top
          animated: true,
        });
      }, 200); // Increased timeout to ensure elements are rendered
    } else {
      console.log('ScrollView ref not found');
    }
  };

   const scrollToOriginalFocus = () => {
    console.log('scrollToPatient called');
    if (scrollViewRef.current) {
      console.log('ScrollView ref exists, scrolling...');
      setTimeout(() => {
        scrollViewRef.current.scrollTo({
          y: 0, // Scroll down to hide header and facility, show patient at top
          animated: true,
        });
      }, 200); // Increased timeout to ensure elements are rendered
    } else {
      console.log('ScrollView ref not found');
    }
  };


  const handleFacilitySelect = (facilityId: string) => {
    setSelectedFacilityId(facilityId);
  };

  const {
    data: patients,
    isLoading: patientsLoading,
    error: patientsError,
  } = usePatients(selectedFacilityId || "", patientNameSearch);

  const handlePatientSelect = (
    patientId: string,
    patientFirstName: string,
    dob: string
  ) => {
    setSelectedPatient({
      id: patientId,
      name: patientFirstName,
      dob,
      facilityId: selectedFacilityId,
    });
    setAddPatientDisabled(false);
  };

  const addPatient = () => {
    onAddpatient(selectedPatient);
    onClose(false);
  };

  const dialogContent = (
    <>
      <View ref={headerRef}>
        <Text {...styles.headerText}>Add a patient to call</Text>
      </View>

      <YStack {...styles.facilityContainer} ref={facilityRef}>
        <Text {...styles.facilityTitle}>Facility</Text>
        <YStack>
          {facilitiesLoading ? (
            <Text>Loading facilities...</Text>
          ) : facilitiesError ? (
            <Text>Error loading facilities</Text>
          ) : (
            <View onFocus={scrollToFacility}>
            <FacilityDrawer
              data={facilitiesss || []}
              placeholder="Select a Facility"
              onSelect={(id: string) => {
                handleFacilitySelect(id);
                  scrollToOriginalFocus();
              }}
            />
            </View>
          )}
        </YStack>
      </YStack>

      <YStack {...styles.facilityContainer}>
        <View ref={patientTitleRef}>
          {ehrType !== "manual" && (
            <Text {...styles.facilityTitle}>Patient</Text>
          )}
          {ehrType === "manual" && (
            <Text {...styles.facilityTitle}>Patient</Text>
          )}
        </View>
        {ehrType === "manual" ? (
          <YStack width="100%">
            <TextArea
              {...styles.patientNameTextArea}
              placeholder="Type Patient Name"
              placeholderTextColor="$textcolor"
              overflow="hidden"
              value={selectedPatient.name}
              onFocus={() => scrollToPatient()}
              onChangeText={(text) => {
                setSelectedPatient((prev) => ({
                  ...prev,
                  name: text,
                  id: text,
                }));
              }}
            />
            <Text {...styles.facilityTitle} mt={"$4"}>
              Date of Birth (MM/DD/YYYY)
            </Text>
            <TextArea
              {...styles.patientNameTextArea}
              placeholder="MM-DD-YYYY"
              placeholderTextColor="$textcolor"
              keyboardType={
                Platform.OS === "ios" ? "number-pad" : "numeric"
              }
              maxLength={10}
              value={selectedPatient.dob}
              onFocus={() => scrollToPatient()}
              onChangeText={(text) => {
                const dob = formatDOB(text);
                setSelectedPatient((prev) => ({
                  ...prev,
                  dob,
                }));
              }}
            />
          </YStack>
        ) : (
          <YStack marginBlockEnd={50}     onFocus={() => scrollToPatient()}>
            <PatientSearchDrawer
              data={patientsss || []}
              placeholder="Select a Patient"
              onSelect={(id: string, firstName: string, dob: string) => {
                handlePatientSelect(id, firstName, dob);
                scrollToOriginalFocus();
              }}
              onSearch={(query: string) => {
                setPatientNameSearch(query);
                return Promise.resolve();
              }}
              disabled={!selectedFacilityId}
              loading={patientsLoading}
              error={ ""}
      
            />
          </YStack>
        )}
      </YStack>

      <Button
        icon={<Plus size={"$1"} color={"white"} />}
        {...(addPatientDisabled
          ? styles.addPatientBtnDisabled
          : styles.addPatientBtn)}
        disabled={addPatientDisabled}
        onPress={addPatient}
      >
        Add
      </Button>

      <Button {...styles.cancelBtn} onPress={() => onClose(false)}>
        Cancel
      </Button>

      {/* Extra spacer to make content scrollable */}
      <View style={{ height: 300 }} />
    </>
  );

  return (
    <Dialog modal open={open} onOpenChange={onClose}>
      <Dialog.Overlay {...styles.overlay} />
      <Dialog.Content {...styles.dialogContent}>
        <ScrollView
          ref={scrollViewRef}
          style={{
            height: 380,
            padding: 10,
          }}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={{ gap: 8 }}>
            {dialogContent}
          </View>
        </ScrollView>
      </Dialog.Content>
    </Dialog>
  );
}

export const formatDOB = (raw: string) => {
  const digits = raw.replace(/\D/g, "").slice(0, 8);
  if (digits.length <= 2) return digits;
  if (digits.length <= 4) {
    return `${digits.slice(0, 2)}-${digits.slice(2)}`;
  }
  return `${digits.slice(0, 2)}-${digits.slice(2, 4)}-${digits.slice(4)}`;
};
