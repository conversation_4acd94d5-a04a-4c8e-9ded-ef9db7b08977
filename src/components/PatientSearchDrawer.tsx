import { Check, ChevronDown } from "@tamagui/lucide-icons";
import React, {
  MutableRefObject,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import {
  ActivityIndicator,
  FlatList,
  Keyboard,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import {
  Square,
  Theme,
  useTheme,
  YStack,
  Text,
  XStack,
  Checkbox,
} from "tamagui";

export interface SelectableItem {
  id: string;
  name: string;
  dob: string;
}

interface PatientSearchDrawerProps {
  data: SelectableItem[] | undefined;
  placeholder?: string;
  onSelect: (id: string, firstName: string, dob: string) => void;
  onOpen?: () => void;
  onClose?: () => void;
  isOpen?: boolean;
  onSearch: (query: string) => Promise<void>;
  loading?: boolean;
  error?: string;
  disabled?: boolean;
}

const PatientSearchDrawer: React.FC<PatientSearchDrawerProps> = ({
  data = [],
  placeholder = "Select a Patient",
  onSelect,
  onOpen,
  onClose,
  isOpen = false,
  onSearch,
  loading = false,
  error = "",
  disabled = false,
}) => {
  const [search, setSearch] = useState<string>("");
  const [clicked, setClicked] = useState<boolean>(false);
  const [selectedPatient, setSelectedPatient] = useState<string>("");
  const searchRef = useRef<TextInput>(null);

  const debounceRef = useRef<number | null>(null) as MutableRefObject<
    number | null
  >;

  const styles = getStyles();

  useEffect(() => {
    setClicked(isOpen);
  }, [isOpen]);

  const handleOpen = () => {
    if (!clicked) {
      setClicked(true);
      onOpen?.();
    }
  };

  const handleClose = () => {
    setClicked(false);
    onClose?.();
  };

  const searchPatients = (query: string) => {
    if (debounceRef.current !== null) {
      clearTimeout(debounceRef.current);
    }
    debounceRef.current = window.setTimeout(() => {
      if (query.length >= 3) {
        setSearch(query);
        onSearch(query);
      } else {
        setSearch("");
      }
    }, 500);
  };

  const handleSelect = useCallback(
    (item: SelectableItem) => {
      onSelect(item.id, item.name, item.dob);
      setSelectedPatient(item.name);
      handleClose();
      setSearch("");
      Keyboard.dismiss();
    },
    [onSelect]
  );

  return (
    <View {...styles.container}>
      {
        <TouchableOpacity
          disabled={disabled}
          {...(disabled
            ? { style: styles.disabledSelectButton }
            : { style: styles.selectButton })}
          onPress={() => {
            clicked ? handleClose() : handleOpen();
            Keyboard.dismiss();
          }}
        >
          <Text
            {...(disabled
              ? { style: styles.disabledSelectedText }
              : { style: styles.selectedText })}
          >
            {selectedPatient === "" ? placeholder : selectedPatient}
          </Text>
          <Square
            animation="quick"
            rotate={clicked ? "180deg" : "0deg"}
            marginInlineStart={10}
          >
            <ChevronDown {...styles.ChevronDown} />
          </Square>
        </TouchableOpacity>
      }

      {clicked && (
        <YStack style={styles.dropdownContainer}>
          <YStack {...styles.dropdownContainerChild}>
            <TextInput
              placeholder="Search by name..."
              autoCorrect={false}
              ref={searchRef}
              onChangeText={searchPatients}
              {...styles.searchInput}
              placeholderTextColor={"#888"}
            />
            {loading ? (
              <View {...styles.loadingContainer}>
                <ActivityIndicator size="small" />
                <Text {...styles.loadingText}>Loading patients...</Text>
              </View>
            ) : error ? (
              <Text {...styles.errorText}>{error}</Text>
            ) : (
              <FlatList
                data={data}
                keyExtractor={(item) => item.id}
                renderItem={({ item }) => (
                  <TouchableOpacity
                    style={styles.listItem}
                    onPress={() => handleSelect(item)}
                  >
                    <Text style={styles.listItemText}>
                      {item.name}
                      {item.dob ? ` (${item.dob})` : ""}
                    </Text>
                  </TouchableOpacity>
                )}
                keyboardShouldPersistTaps="handled"
                ListEmptyComponent={() => (
                  <Text {...styles.emptyText}>
                    {search.length >= 3
                      ? "No results found"
                      : "Type at least 3 letters to search"}
                  </Text>
                )}
              />
            )}
          </YStack>
        </YStack>
      )}
    </View>
  );
};

const getStyles = () => {
  const theme = useTheme();
  return {
    container: {
      width: "100%",
      position: "absolute",
    },
    selectButton: {
      width: "100%" as any,
      minHeight: 50,
      borderRadius: 10,
      borderWidth: 0.5,
      borderColor: theme.primaryBorderColor.val,
      flexDirection: "row" as any,
      alignItems: "center" as any,
      justifyContent: "space-between" as any,
      paddingHorizontal: 15,
      backgroundColor: "transparent",
      paddingVertical: 15,
      flexWrap: "wrap" as any,
    },
    disabledSelectButton: {
      width: "100%" as any,
      minHeight: 50,
      borderRadius: 10,
      borderWidth: 0.5,
      borderColor: theme.disbaledSelectedButtonBorderColor.val,
      flexDirection: "row" as any,
      alignItems: "center" as any,
      justifyContent: "space-between" as any,
      paddingHorizontal: 15,
      backgroundColor: theme.disbaledSelectedButtonBackgroundColor.val,
      paddingVertical: 15,
      flexWrap: "wrap" as any,
    },

    selectedText: {
      fontWeight: "500" as any,
      fontSize: 16,
      color: theme.textcolor.val,
      flex: 1,
      flexWrap: "wrap",
    },
    disabledSelectedText: {
      fontWeight: "500" as any,
      fontSize: 16,
      color: theme.disabledSelectedTextColor.val,
      flex: 1,
      flexWrap: "wrap" as any,
    },

    dropdownContainer: {
      zIndex: 999,
      maxHeight: 140,
      top: 10,
      shadowColor: "#000" as any,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.1,
      shadowRadius: 5,
      elevation: 1,
      backgroundColor: "transparent",
    },
    dropdownContainerChild: {
      borderWidth: 1,
      borderColor: theme.primaryBorderColor.val as any,
      borderRadius: 8,
      backgroundColor: theme.patientDropDownBackgroundColor.val,
    },
    searchInput: {
      height: 50,
      borderWidth: 0.5,
      borderColor: theme.primaryBorderColor.val,
      paddingHorizontal: 15,
      borderRadius: 7,
      backgroundColor: theme.screenBackgroundcolor.val,
      margin: 15,
      color: theme.textcolor.val,
    },
    listItem: {
      paddingVertical: 15,
      paddingHorizontal: 20,
    },
    listItemText: {
      fontWeight: "600" as any,
      color: theme.textcolor.val as any,
      marginStart: 5,
    },
    listItemContainer: {
      alignItems: "center",
    },
    checkbox: {
      backgroundColor: "transparent" as any,
      borderColor: "$primaryBorderColor" as any,
    },
    emptyText: {
      textAlign: "center",
      padding: 10,
      fontSize: 16,
      color: theme.emptyTextColor.val as any,
    },
    loadingContainer: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 20,
      paddingVertical: 15,
    },
    loadingText: {
      marginLeft: 10,
      fontSize: 16,
      color: theme.textcolor.val as any,
    },
    errorText: {
      textAlign: "center",
      padding: 10,
      fontSize: 16,
      color: "red" as any,
    },
    ChevronDown: {
      size: 20,
      color: theme.textcolor.val as any,
    },
  };
};

export default PatientSearchDrawer;
