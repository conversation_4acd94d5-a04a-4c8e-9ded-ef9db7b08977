export const useAddpatientDialogStyle = () => {
  return {
    dialogContent: {
      bordered: true,
      bg: "$screenBackgroundcolor" as any,
      br: "$4" as any,
      shadowColor: "transparent" as any,
      shadowOpacity: 0 as any,
      shadowRadius: 0 as any,
      width: "90%" as any,
      maxHeight: 420 as any,
      alignSelf: "center" as any,
      borderRadius: "$7" as any,
      position: "absolute" as any,
      left: "50%" as any,
      transform: [{ translateX: "-50%" }, { translateY: "-50%" }] as any,
      borderColor: "$primaryBorderColor" as any,
    },
    container: {
      gap: "$2" as any,
      overflow: "scroll" as any,
      height: 390, // Fixed height smaller than dialog to allow scrolling
      padding: 10,
    },
    headerText: {
      fontSize: 24,
      fontWeight: "600" as any,
    },
    overlay: {
      animation: "quick" as any,
      enterStyle: { opacity: 0 },
      exitStyle: { opacity: 0 },
      backgroundColor: "black" as any,
      opacity: 0.6,
    },
    cancelBtn: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
      borderRadius: 8,
      color: "$textcolor" as any,
      fontWeight: "600" as any,
      size: "$4" as any,
      fontSize: 16,
    },
    facilityContainer: {
      marginBlockStart: "$4" as any,
      width: "100%" as "100%",
    },
    facilityTitle: {
      fontWeight: 500 as any,
      fontSize: 14,
      marginBlockEnd: 10,
    },
    patientNameTextArea: {
      backgroundColor: "$screenBackgroundcolor" as any,
      borderColor: "$primaryBorderColor" as any,
      width: "100%" as "100%",
      borderWidth: 1,
      borderRadius: 7,
      color: "$textcolor" as any,
      fontWeight: 500 as any,
      padding: 10,
      fontSize: 16,
      numberOfLines: 6,
      textAlignVertical: "top" as any,
    },

    addPatientBtnDisabled: {
      backgroundColor: "$disableButtonPrimaryColor" as any,
      borderColor: "$disableButtonPrimaryColor" as any,
      borderRadius: 8,
      color: "$buttonWhiteColor" as any,
      fontWeight: "600" as any,
      size: "$4" as any,
      fontSize: 16,
      marginBlockStart: 15,
    },
    addPatientBtn: {
      backgroundColor: "$primaryColor" as any,
      borderColor: "$primaryColor" as any,
      borderRadius: 8,
      color: "$buttonWhiteColor" as any,
      fontWeight: "600" as any,
      size: "$4" as any,
      fontSize: 16,
      marginBlockStart: 15,
    },
  };
};
