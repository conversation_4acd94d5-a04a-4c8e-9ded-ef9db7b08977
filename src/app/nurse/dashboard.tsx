import React, { useCallback, useEffect, useState } from "react";
import { Button, Dialog, Image, Text, View, XStack, YStack } from "tamagui";
import Header from "@/common/header";
import SearchInput from "src/components/SearchInput";
import SheetDemo from "src/components/SettingsDrawer";
import Consultations from "./consultations";
import { useDashboardStyles } from "./Styles/DashboardStyle";
import { useRouter } from "expo-router";
import { Pressable } from "react-native";
import { User, Users } from "@tamagui/lucide-icons";

export default function Dashboard() {
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500);

    return () => clearTimeout(handler);
  }, [searchQuery]);

  const router = useRouter();

  const handlePress = useCallback(() => setOpen(true), []);
  const requestNewVisit = () => {
    setRequestVisitDialogOpen(true);
  };
  const styles = useDashboardStyles();
  const [requestVisitDialogOpen, setRequestVisitDialogOpen] = useState(false);
  const singlePatient = () => {
    router.push({
      pathname: "/nurse/requestVisit",
      params: { isSinglePatient: 1 },
    });
    setRequestVisitDialogOpen(false);
  };
  const multiplePatients = () => {
    router.push({
      pathname: "/nurse/requestVisit",
      params: { isSinglePatient: 0 },
    });
    setRequestVisitDialogOpen(false);
  };

  return (
    <View {...styles.container}>
      <YStack {...styles.mainStack}>
        <Header onAvatarPress={handlePress} />
        <Button
          {...styles.requestButton}
          icon={
            <Image
              source={require("../../assets/images/request-new-visit-icon.png")}
              {...styles.requestIcon}
            />
          }
          onPress={requestNewVisit}
        >
          Request New Visit
        </Button>
        <Text {...styles.consultationTitle}>Consultations</Text>
        <YStack {...styles.searchContainer}>
          <SearchInput onSearchChange={setSearchQuery} />
          <Text {...styles.todayLabel}>TODAY</Text>
        </YStack>
        <YStack flex={1}>
          <Consultations searchQuery={debouncedSearchQuery} />
        </YStack>
        {open && <SheetDemo open={open} setOpen={setOpen} />}
      </YStack>
      {requestVisitDialogOpen && (
        <RequestVisitDialog
          open={requestVisitDialogOpen}
          onClose={setRequestVisitDialogOpen}
          singlePatient={singlePatient}
          multiplePatients={multiplePatients}
        />
      )}
    </View>
  );
}

interface RequestVisitDialogProps {
  open: boolean;
  onClose: (open: boolean) => void;
  singlePatient: () => void;
  multiplePatients: () => void;
}

function RequestVisitDialog({
  open,
  onClose,
  singlePatient,
  multiplePatients,
}: RequestVisitDialogProps) {
  const styles = useDashboardStyles();

  const renderOptionCard = (
    IconComponent: React.ElementType,
    label: string,
    onPress: () => void
  ) => (
    <Pressable onPress={onPress} {...styles.card}>
      <YStack {...styles.cardBody}>
        <IconComponent {...styles.icon} />
        <Text {...styles.labeltext}>{label}</Text>
      </YStack>
    </Pressable>
  );

  return (
    <Dialog modal open={open} onOpenChange={onClose}>
      <Dialog.Overlay {...styles.overlay} />
      <Dialog.Content {...styles.dialogContent}>
        <YStack {...styles.dialogContainer}>
          <XStack {...styles.headerContainer}>
            <Text {...styles.headerText}>Type of call</Text>
          </XStack>
          <XStack {...styles.dialogCardContainer}>
            {renderOptionCard(User, "Single patient call", singlePatient)}
            {renderOptionCard(Users, "Multi-patient call", multiplePatients)}
          </XStack>
          <Button onPress={() => onClose(false)} {...styles.cancelBtn}>
            Cancel
          </Button>
        </YStack>
      </Dialog.Content>
    </Dialog>
  );
}
